#!/usr/bin/env python3
from collections import defaultdict
from pathlib import Path

import setuptools
from setuptools import setup

this_dir = Path(__file__).parent
module_dir = this_dir / "piper_train"

# -----------------------------------------------------------------------------

# Load README in as long description
long_description: str = ""
readme_path = this_dir / "README.md"
if readme_path.is_file():
    long_description = readme_path.read_text(encoding="utf-8")

requirements = []
requirements_path = this_dir / "requirements.txt"
if requirements_path.is_file():
    with open(requirements_path, "r", encoding="utf-8") as requirements_file:
        requirements = requirements_file.read().splitlines()

version_path = module_dir / "VERSION"
with open(version_path, "r", encoding="utf-8") as version_file:
    version = version_file.read().strip()

# -----------------------------------------------------------------------------

setup(
    name="piper_train",
    version=version,
    description="A fast and local neural text to speech system",
    long_description=long_description,
    url="http://github.com/rhasspy/piper",
    author="Michael Hansen",
    author_email="<EMAIL>",
    license="MIT",
    packages=setuptools.find_packages(),
    package_data={
        "piper_train": ["VERSION", "py.typed"],
    },
    install_requires=requirements,
    extras_require={':python_version<"3.9"': ["importlib_resources"]},
    entry_points={
        "console_scripts": [
            "piper-train = piper_train.__main__:main",
        ]
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "Topic :: Text Processing :: Linguistic",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
    ],
    keywords="rhasspy tts speech voice",
)
